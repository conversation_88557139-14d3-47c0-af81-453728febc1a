import express from "express";
import { convertTextToGig } from "../services/gigConversionService.js";
import { asyncHandler } from "../middleware/errorHandler.js";
import {
  extractTextFromFile,
  sanitizeExtractedText,
} from "../services/fileProcessor.js";
import { uploadSingle, handleUploadError } from "../middleware/upload.js";
import { validateConvertRequest } from "../middleware/validation.js";
import { requireAuth } from "../middleware/auth.js";

const router = express.Router();

// Convert text to gig route
router.post(
  "/text-to-gig",
  requireAuth,
  validateConvertRequest,
  asyncHandler(async (req, res) => {
    const { text } = req.body;

    const convertedData = await convertTextToGig(text);

    res.json(convertedData);
  })
);

// Upload and convert file to gig route
router.post(
  "/upload-and-convert",
  requireAuth,
  uploadSing<PERSON>,
  handleUploadError,
  asyncHandler(async (req, res) => {
    // Check if file was uploaded
    if (!req.file) {
      return res.status(400).json({
        error: "No file uploaded",
        message: "Please select a file to upload",
      });
    }

    const { path: filePath, originalname } = req.file;

    try {
      // Extract text from the uploaded file
      const extractedText = await extractTextFromFile(filePath, originalname);
      const cleanText = sanitizeExtractedText(extractedText);

      if (!cleanText || cleanText.length < 10) {
        return res.status(400).json({
          error: "Insufficient content",
          message:
            "The uploaded file does not contain enough readable text content",
        });
      }

      // Convert text to gig format using the service
      const convertResult = await convertTextToGig(cleanText);
      console.log("clean text", cleanText);
      console.log("Converted gig:", convertResult);

      // Return the converted gig data along with file info
      res.json({
        success: true,
        gig: convertResult,
        fileInfo: {
          originalName: originalname,
          size: req.file.size,
          type: req.file.mimetype,
        },
        message: "File uploaded and converted successfully",
      });
    } catch (error) {
      console.error("File upload and conversion error:", error);

      return res.status(500).json({
        error: "Processing failed",
        message:
          "An error occurred while processing your file. Please try again.",
      });
    }
  })
);

export default router;
