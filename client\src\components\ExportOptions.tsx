import React, { useState } from "react";
import { ExportFormat, ExportState, EXPORT_FORMATS } from "../types/export";
import { Gig } from "../types/gig";

interface ExportOptionsProps {
  gigData: Gig;
  onExport: (format: ExportFormat["type"]) => Promise<void>;
  exportState: ExportState;
  className?: string;
}

const ExportOptions: React.FC<ExportOptionsProps> = ({
  gigData,
  onExport,
  exportState,
  className = "",
}) => {
  const [selectedFormat, setSelectedFormat] =
    useState<ExportFormat["type"]>("pdf");
  const [showOptions, setShowOptions] = useState(false);

  const handleExport = async () => {
    if (!exportState.isExporting) {
      await onExport(selectedFormat);
    }
  };

  const hasContent = gigData && (gigData.title || gigData.summary);

  if (!hasContent) {
    return (
      <div
        className={`p-4 border border-gray-300 rounded-lg bg-gray-50 ${className}`}
      >
        <div className="text-center text-gray-500">
          <div className="text-3xl mb-2">📄</div>
          <p className="m-0 text-sm">
            Create your gig content first to enable export options
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`p-6 border border-gray-300 rounded-lg bg-white ${className}`}
    >
      <div className="mb-6">
        <h3 className="m-0 mb-2 text-xl font-semibold text-gray-900">
          Export Your Gig
        </h3>
        <p className="m-0 text-sm text-gray-500">
          Download your gig description in your preferred format
        </p>
      </div>

      <div className="flex flex-col gap-6">
        {/* Format Selection */}
        <div className="flex flex-col gap-4">
          <label className="font-medium text-gray-700">
            Choose Export Format:
          </label>
          <div className="flex flex-col gap-3">
            {EXPORT_FORMATS.map((format) => (
              <label
                key={format.type}
                className="flex items-center gap-3 p-3 border border-gray-300 rounded-md cursor-pointer transition-all duration-200 hover:border-blue-500 hover:bg-slate-50"
              >
                <input
                  type="radio"
                  name="exportFormat"
                  value={format.type}
                  checked={selectedFormat === format.type}
                  onChange={(e) =>
                    setSelectedFormat(e.target.value as ExportFormat["type"])
                  }
                  disabled={exportState.isExporting}
                  className="m-0"
                />
                <div className="flex-1">
                  <div className="font-medium text-gray-700">{format.name}</div>
                  <div className="text-sm text-gray-500">
                    {format.description}
                  </div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Export Actions */}
        <div className="flex gap-3 flex-wrap">
          <button
            type="button"
            className="flex items-center gap-2 bg-blue-500 text-white border-none px-6 py-3 rounded-md cursor-pointer font-medium transition-colors duration-200 hover:bg-blue-700 disabled:opacity-60 disabled:cursor-not-allowed"
            onClick={handleExport}
            disabled={exportState.isExporting}
          >
            {exportState.isExporting ? (
              <>
                <span className="animate-spin">⏳</span>
                Exporting...
              </>
            ) : (
              <>
                <span>📥</span>
                Export as{" "}
                {EXPORT_FORMATS.find((f) => f.type === selectedFormat)?.name}
              </>
            )}
          </button>

          <button
            type="button"
            className="bg-gray-100 text-gray-700 border border-gray-300 px-4 py-3 rounded-md cursor-pointer transition-colors duration-200 hover:bg-gray-200 disabled:opacity-60 disabled:cursor-not-allowed"
            onClick={() => setShowOptions(!showOptions)}
            disabled={exportState.isExporting}
          >
            {showOptions ? "Hide" : "Show"} Preview
          </button>
        </div>

        {/* Export Status */}
        {exportState.success && (
          <div className="flex items-center gap-2 p-3 rounded-md bg-green-100 text-green-800 border border-green-200">
            <div>✅</div>
            <div>
              Export completed successfully! Your download should start
              automatically.
            </div>
          </div>
        )}

        {exportState.error && (
          <div className="flex items-center gap-2 p-3 rounded-md bg-red-100 text-red-800 border border-red-200">
            <div>❌</div>
            <div>{exportState.error}</div>
          </div>
        )}

        {/* Content Preview */}
        {showOptions && (
          <div className="border-t border-gray-300 pt-6">
            <h4 className="m-0 mb-4 text-base font-semibold text-gray-700">
              Content Preview
            </h4>
            <div className="flex flex-col gap-3">
              {gigData.title && (
                <div className="p-2 bg-gray-50 rounded text-sm leading-relaxed">
                  <strong className="text-gray-700">Title:</strong>{" "}
                  {gigData.title}
                </div>
              )}
              {gigData.summary && (
                <div className="p-2 bg-gray-50 rounded text-sm leading-relaxed">
                  <strong className="text-gray-700">Summary:</strong>{" "}
                  {gigData.summary.substring(0, 100)}
                  {gigData.summary.length > 100 ? "..." : ""}
                </div>
              )}
              {gigData.deliverables && (
                <div className="p-2 bg-gray-50 rounded text-sm leading-relaxed">
                  <strong className="text-gray-700">Deliverables:</strong>{" "}
                  {gigData.deliverables.substring(0, 100)}
                  {gigData.deliverables.length > 100 ? "..." : ""}
                </div>
              )}
              {gigData.skills && (
                <div className="p-2 bg-gray-50 rounded text-sm leading-relaxed">
                  <strong className="text-gray-700">Skills:</strong>{" "}
                  {gigData.skills.substring(0, 100)}
                  {gigData.skills.length > 100 ? "..." : ""}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExportOptions;
