import { useState } from "react";
import { ExportFormat, EXPORT_FORMATS } from "../types/export";
import { useGig } from "../contexts/GigContext";
import { useFileOperations } from "../contexts/FileContext";
import apiService from "../services/apiService";

const ExportOptions = () => {
  const { state: gigState } = useGig();
  const { exportState, setExportLoading, setExportError, setExportSuccess } =
    useFileOperations();

  const [selectedFormat, setSelectedFormat] =
    useState<ExportFormat["type"]>("pdf");

  const handleExport = async () => {
    if (!exportState.isExporting) {
      setExportLoading(true);

      try {
        const blob = await apiService.exportGig(gigState.gig, selectedFormat);

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;

        // Set filename based on format
        const timestamp = new Date().toISOString().split("T")[0];
        const filename = `gig-description-${timestamp}.${selectedFormat}`;
        link.download = filename;

        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        setExportSuccess(true, selectedFormat);
      } catch (error) {
        setExportError(
          error instanceof Error ? error.message : "Export failed"
        );
      }
    }
  };

  const hasContent =
    gigState.gig && (gigState.gig.title || gigState.gig.summary);

  if (!hasContent) {
    return (
      <div className={`p-4 border border-gray-300 rounded-lg bg-gray-50`}>
        <div className="text-center text-gray-500">
          <div className="text-3xl mb-2">📄</div>
          <p className="m-0 text-sm">
            Create your gig content first to enable export options
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-6 border border-gray-300 rounded-lg bg-white`}>
      <div className="mb-6">
        <h3 className="m-0 mb-2 text-xl font-semibold text-gray-900">
          Export Your Gig
        </h3>
        <p className="m-0 text-sm text-gray-500">
          Download your gig description in your preferred format
        </p>
      </div>

      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-4">
          <label className="font-medium text-gray-700">
            Choose Export Format:
          </label>
          <div className="flex flex-col gap-3">
            {EXPORT_FORMATS.map((format) => (
              <label
                key={format.type}
                className="flex items-center gap-3 p-3 border border-gray-300 rounded-md cursor-pointer transition-all duration-200 hover:border-blue-500 hover:bg-slate-50"
              >
                <input
                  type="radio"
                  name="exportFormat"
                  value={format.type}
                  checked={selectedFormat === format.type}
                  onChange={(e) =>
                    setSelectedFormat(e.target.value as ExportFormat["type"])
                  }
                  disabled={exportState.isExporting}
                  className="m-0"
                />
                <div className="flex-1">
                  <div className="font-medium text-gray-700">{format.name}</div>
                  <div className="text-sm text-gray-500">
                    {format.description}
                  </div>
                </div>
              </label>
            ))}
          </div>
        </div>

        <div className="flex gap-3 flex-wrap">
          <button
            type="button"
            className="flex items-center gap-2 bg-blue-500 text-white border-none px-6 py-3 rounded-md cursor-pointer font-medium transition-colors duration-200 hover:bg-blue-700 disabled:opacity-60 disabled:cursor-not-allowed"
            onClick={handleExport}
            disabled={exportState.isExporting}
          >
            {exportState.isExporting ? (
              <>
                <span className="animate-spin">⏳</span>
                Exporting...
              </>
            ) : (
              <>
                <span>📥</span>
                Export as{" "}
                {EXPORT_FORMATS.find((f) => f.type === selectedFormat)?.name}
              </>
            )}
          </button>
        </div>

        {exportState.success && (
          <div className="flex items-center gap-2 p-3 rounded-md bg-green-100 text-green-800 border border-green-200">
            <div>✅</div>
            <div>
              Export completed successfully! Your download should start
              automatically.
            </div>
          </div>
        )}

        {exportState.error && (
          <div className="flex items-center gap-2 p-3 rounded-md bg-red-100 text-red-800 border border-red-200">
            <div>❌</div>
            <div>{exportState.error}</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExportOptions;
